// types/flight.ts
export interface Airline {
  name: string;
  logo: string; // URL
}

export interface LegInfo {
  time: string; 
  date: string; 
  location: string; 
}

export interface BaggageInfo {
  cabin: string; 
  checkin: string;
}

export interface Flight {
  id: string | number;
  from: string;
  to: string;
  duration: string; 
  airlines: Airline[];
  departure: LegInfo;
  arrival: LegInfo;
  stops: string; 
  stopDetails?: string;
  baggage: BaggageInfo;
}
