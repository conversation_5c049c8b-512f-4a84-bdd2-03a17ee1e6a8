'use client';

import { useState } from 'react';
import { Button, Tooltip, Select, SelectItem } from '@heroui/react';
import Image from 'next/image';

import { InfoIcon } from '@/components/icons';

export default function PhotoExpenseCard() {
  const [currency] = useState('$');

  return (
    <div className="max-w-6xl mx-auto  rounded-2xl overflow-hidden mb-4">
      <div className="flex flex-col md:flex-row gap-2">
        <div className="flex flex-col md:flex-row md:w-2/3 gap-2">
          {/* Large Image */}
          <div className="md:w-2/3">
            <Image
              src="https://heroui.com/images/hero-card-complete.jpeg"
              alt="Large Image"
              width={800}
              height={300}
              className="w-full h-full object-cover rounded-none md:rounded-l-xl"
            />
          </div>

          {/* Two Small Images */}
          <div className="md:w-1/3 flex flex-col gap-2">
            <Image
              src="https://heroui.com/images/hero-card-complete.jpeg"
              alt="Small Image 1"
              width={400}
              height={200}
              className="w-full h-[150px] object-cover rounded-tr-xl"
            />

            <div className="relative">
              <Image
                src="https://heroui.com/images/hero-card-complete.jpeg"
                alt="Small Image 2"
                width={400}
                height={200}
                className="w-full h-[150px] object-cover rounded-br-xl"
              />
              <Button
                size="sm"
                className="absolute bottom-2 right-2 bg-white text-black text-xs px-3 py-1 rounded-full shadow-md hover:bg-gray-100"
                variant="flat"
              >
                View more
              </Button>
            </div>
          </div>
        </div>

        <div className="md:w-1/3 bg-white rounded-xl flex flex-col justify-center items-center px-4 py-10 gap-6 ">
          <div className="text-center w-full">
            <div className="text-lg font-semibold mb-4 flex items-center gap-1">
              Expense Estimate
              <Tooltip content="I am a tooltip">
                <div className="cursor-pointer text-gray-500 hover:text-gray-700">
                  <InfoIcon size={18} />
                </div>
              </Tooltip>
            </div>

            <div className="flex justify-center items-center gap-3 bg-white border rounded-md px-4 w-full py-3 mx-auto">
              <Select
                value={currency}
                // onChange={(val: string) => setCurrency(val)}
                className="w-[45px] text-sm h-8"
              >
                <SelectItem key="$">$</SelectItem>
                <SelectItem key="€">€</SelectItem>
                <SelectItem key="₹">₹</SelectItem>
              </Select>
              <span className="text-xl font-semibold text-gray-800">
                999.00
              </span>
            </div>
          </div>

          <p className="text-sm text-gray-500 text-center">
            Please login to customise expense
          </p>

          <Button
            size="lg"
            className="w-full max-w-sm rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold shadow-md hover:opacity-90 transition"
          >
            Get Started
          </Button>
        </div>
      </div>
    </div>
  );
}
